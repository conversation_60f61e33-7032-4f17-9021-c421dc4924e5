<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Card, Toggle } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import Fa from 'svelte-fa';
	import { faUser, faKey, faCircleExclamation, faExclamationTriangle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';
	import { Field, Control, FieldErrors } from 'formsnap';
	import { Alert, Spinner } from 'flowbite-svelte';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;

	// Client-side validation state for username
	let usernameValidationError = '';
	let usernameBlurred = false;

	// Rate limiting state
	let isRateLimited = false;
	let rateLimitCountdown = 0;
	let rateLimitMessage = '';
	let countdownInterval: NodeJS.Timeout | null = null;

	// Username input handler with filtering and validation
	function handleUsernameInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredValue = target.value.replace(/[^a-zA-Z0-9._-]/g, '').replace(/--/g, '-');

		// Update form data with filtered value
		$formData.username = filteredValue;

		// Clear validation error when user types valid characters
		if (filteredValue.length > 0) {
			usernameValidationError = '';
		} else if (usernameBlurred) {
			// Show required error if field is empty and was blurred
			usernameValidationError = 'Username is required';
		}

		// Show error if invalid characters were filtered out
		if (filteredValue !== target.value && filteredValue.length > 0) {
			usernameValidationError = 'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input value to reflect the filtered value
		target.value = filteredValue;
	}

	// Handle username blur event
	function handleUsernameBlur(event: Event) {
		const target = event.target as HTMLInputElement;
		usernameBlurred = true;
		
		if (target.value.trim() === '') {
			usernameValidationError = 'Username is required';
		}
	}

	// Handle paste events to filter pasted content
	function handleUsernamePaste(event: ClipboardEvent) {
		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';
		
		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredText = pastedText.replace(/[^a-zA-Z0-9._-]/g, '').replace(/--/g, '-');

		// Update form data with filtered value
		$formData.username = filteredText;

		// Clear validation errors if we have valid content
		if (filteredText.length > 0) {
			usernameValidationError = '';
		}

		// Show validation message if content was filtered
		if (filteredText !== pastedText && filteredText.length > 0) {
			usernameValidationError = 'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input element directly
		const target = event.target as HTMLInputElement;
		target.value = filteredText;

		// Trigger input event to ensure reactivity
		target.dispatchEvent(new Event('input', { bubbles: true }));
	}

	// Rate limiting functions
	function startRateLimitCountdown(seconds: number) {
		isRateLimited = true;
		rateLimitCountdown = seconds;
		
		if (countdownInterval) {
			clearInterval(countdownInterval);
		}
		
		countdownInterval = setInterval(() => {
			rateLimitCountdown--;
			if (rateLimitCountdown <= 0) {
				clearRateLimit();
			}
		}, 1000);
	}

	function clearRateLimit() {
		isRateLimited = false;
		rateLimitCountdown = 0;
		rateLimitMessage = '';
		if (countdownInterval) {
			clearInterval(countdownInterval);
			countdownInterval = null;
		}
	}

	function checkForRateLimit(response: Response) {
		if (response?.form?.message?.status === 'fail') {
			const detail = response.form.message.detail || '';
			
			// Check for rate limiting indicators in the response
			if (detail.toLowerCase().includes('rate limit') || 
				detail.toLowerCase().includes('too many attempts') ||
				detail.toLowerCase().includes('try again')) {
				
				rateLimitMessage = detail;
				
				// Try to extract time from message (e.g., "try again in 60 seconds")
				const timeMatch = detail.match(/(\d+)\s*(second|minute|hour)/i);
				if (timeMatch) {
					const value = parseInt(timeMatch[1]);
					const unit = timeMatch[2].toLowerCase();
					
					let seconds = value;
					if (unit.startsWith('minute')) seconds *= 60;
					if (unit.startsWith('hour')) seconds *= 3600;
					
					startRateLimitCountdown(seconds);
				} else {
					// Default countdown if no time specified
					startRateLimitCountdown(60);
				}
			}
		}
	}

	// Reactive statement to check for rate limiting on response changes
	$: if (response) {
		checkForRateLimit(response);
	}
</script>

<!-- <SuperDebug data={response} /> -->
<!-- {#if response?.form?.message?.status == 'fail'}
	<Alert border>
		<div class="flex items-center gap-3">
			<Fa icon={faCircleExclamation} />
			<span class="text-lg font-medium">Login failed</span>
		</div>
		<p class="mt-2 text-sm">
			{response?.form?.message?.detail}
		</p>
	</Alert>
{/if} -->
<!-- Main Container -->
<!-- <br /> -->

<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="dark:text-white mb-5 text-left text-5xl font-bold text-gray-700">Salmate</h3>
			<br />
			<Field {form} name="username">
				<Control let:attrs>
					<Label class="text-left" for="username">Username</Label>
					<div class="relative mb-2">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<Fa icon={faUser} class="text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="username"
							name="username"
							type="text"
							bind:value={$formData.username}
							placeholder="Enter your username"
							disabled={isRateLimited}
							class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2 {usernameValidationError
								? 'border-red-500 focus:ring-red-500'
								: ''} {isRateLimited ? 'opacity-50 cursor-not-allowed' : ''}"
							on:input={handleUsernameInput}
							on:blur={handleUsernameBlur}
							on:paste={handleUsernamePaste}
							maxlength="20"
						/>
						<div class="absolute -bottom-8 left-0 right-0">
							{#if usernameValidationError}
								<div class="flex items-center gap-1 text-xs text-red-600 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded-md border border-red-200 dark:border-red-800 animate-in slide-in-from-top-2 duration-200">
									<Fa icon={faTimesCircle} class="text-red-500 flex-shrink-0" />
									<span class="font-medium">{usernameValidationError}</span>
								</div>
							{:else}
								<div class="text-xs text-red-600">
									<FieldErrors />
								</div>
							{/if}
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<Field {form} name="password">
				<Control let:attrs>
					<Label class="text-left" for="password">Password</Label>
					<div class="relative mb-6">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<Fa icon={faKey} class="text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="password"
							name="password"
							type="password"
							bind:value={$formData.password}
							placeholder="Enter your password"
							disabled={isRateLimited}
							class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2 {isRateLimited ? 'opacity-50 cursor-not-allowed' : ''}"
						/>
						<div class="absolute -bottom-4 left-0 right-0">
							<div class="text-xs text-red-600">
								<FieldErrors />
							</div>
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<div class="flex flex-col gap-0">
				<Button
					type="submit"
					disabled={isRateLimited}
					class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600 {isRateLimited ? 'opacity-50 cursor-not-allowed' : ''}"
				>
					{#if $delayed}
						<Spinner class="me-3" size="4" color="white" data-testid="loading-spinner" /> Logging In
					{:else if isRateLimited}
						Try again in {rateLimitCountdown}s
					{:else}
						Login
					{/if}
				</Button>
				{#if isRateLimited}
					<div class="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg animate-in slide-in-from-top-2 duration-200">
						<div class="flex items-center gap-2 text-orange-700 dark:text-orange-300">
							<Fa icon={faExclamationTriangle} class="text-orange-500 flex-shrink-0" />
							<div>
								<p class="font-medium text-sm">Rate Limited</p>
								<p class="text-xs text-orange-600 dark:text-orange-400 mt-1">
									{rateLimitMessage || 'Too many login attempts. Please wait before trying again.'}
								</p>
							</div>
						</div>
					</div>
				{:else if response?.form?.message?.status == 'fail'}
					<div class="text-medium text-red-500">
						{response?.form?.message?.detail || 'Invalid username or password'}
					</div>
				{/if}
			</div>
		</form>
	</div>
</div>
